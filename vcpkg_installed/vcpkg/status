Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-linux
Multi-Arch: same
Abi: 4964c99aba9675e96fcf2e0da2973632879863d9a42b047b92d248df0fdca760
Status: install ok installed

Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-linux
Multi-Arch: same
Abi: 764969545aa87c5bf5bbbbe075296e2cf6b505a45c1111c6384a3c32d9181a8f
Status: install ok installed

Package: catch2
Version: 3.9.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: b0a8918b9c69ee35c8bd0d33e9bd816f6cffdb3d690f657ef184e937485061a6
Description: A modern, C++-native, test framework for unit-tests, TDD and BDD.
Status: install ok installed

Package: egl-registry
Version: 2024-01-25
Architecture: x64-linux
Multi-Arch: same
Abi: 129ac93350da7f8ceca6e07a521ce1594d43875c8e147d033e284812be4adf44
Description: EGL API and Extension Registry
Status: install ok installed

Package: opengl-registry
Version: 2024-02-10
Port-Version: 1
Depends: egl-registry
Architecture: x64-linux
Multi-Arch: same
Abi: 76747cb9051ae8ea9d1c00318997f36e7badad48b3f2f2853f80e1cc1bb4c137
Description: OpenGL, OpenGL ES, and OpenGL ES-SC API and Extension Registry
Status: install ok installed

Package: opengl
Version: 2022-12-04
Port-Version: 3
Depends: opengl-registry
Architecture: x64-linux
Multi-Arch: same
Abi: e596ad348042b84b0380dcfb60e3602dc06fc724904ee9215eabc294f6256781
Description: Open Graphics Library (OpenGL)[3][4][5] is a cross-language, cross-platform application programming interface (API) for rendering 2D and 3D vector graphics.
Status: install ok installed

Package: stb
Version: 2024-07-29
Port-Version: 1
Architecture: x64-linux
Multi-Arch: same
Abi: f1dab91e8709e3b8729e65046151d931c03c7b55f56186fc420f99eba176cd75
Description: public domain header-only libraries
Status: install ok installed

Package: zlib
Version: 1.3.1
Depends: vcpkg-cmake
Architecture: x64-linux
Multi-Arch: same
Abi: 92b722029936f50f445814831f0848c5a16c8e555d4a1b48047a4f3752190cff
Description: A compression library
Status: install ok installed

Package: libpng
Version: 1.6.50
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-linux
Multi-Arch: same
Abi: faf72d55c5a31e40e1fce9ed4c073c583d3e05307894fcfe1cfa769930a9fefc
Description: libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files
Status: install ok installed

Package: bzip2
Version: 1.0.8
Port-Version: 6
Depends: vcpkg-cmake
Architecture: x64-linux
Multi-Arch: same
Abi: 59dc8ffd7ee7294c57b1f76d0f5e3fd1f11f13596d433ff5027e7115d4abbaa8
Description: bzip2 is a freely available, patent free, high-quality data compressor. It typically compresses files to within 10% to 15% of the best available techniques (the PPM family of statistical compressors), whilst being around twice as fast at compression and six times faster at decompression.
Default-Features: tool
Status: install ok installed

Package: bzip2
Feature: tool
Architecture: x64-linux
Multi-Arch: same
Description: Builds bzip2 executable
Status: install ok installed

Package: brotli
Version: 1.1.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: e3dd4df0e4e3579d134601aee2801e4f5890ac5e8cdc96537fb5cdf5c5057de7
Description: a generic-purpose lossless compression algorithm that compresses data using a combination of a modern variant of the LZ77 algorithm, Huffman coding and 2nd order context modeling.
Status: install ok installed

Package: freetype
Version: 2.13.3
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: 3d8c47338df2a3359a431dd9dbb1fdf17ea9f71d84812a67f1c718baaaf4ecfe
Description: A library to render fonts.
Default-Features: brotli, bzip2, png, zlib
Status: install ok installed

Package: freetype
Feature: brotli
Depends: brotli
Architecture: x64-linux
Multi-Arch: same
Description: Support decompression of WOFF2 streams
Status: install ok installed

Package: freetype
Feature: bzip2
Depends: bzip2
Architecture: x64-linux
Multi-Arch: same
Description: Support bzip2 compressed fonts.
Status: install ok installed

Package: freetype
Feature: png
Depends: libpng
Architecture: x64-linux
Multi-Arch: same
Description: Support PNG compressed OpenType embedded bitmaps.
Status: install ok installed

Package: freetype
Feature: zlib
Depends: zlib
Architecture: x64-linux
Multi-Arch: same
Description: Use zlib instead of internal library for DEFLATE
Status: install ok installed

Package: miniaudio
Version: 0.11.22
Architecture: x64-linux
Multi-Arch: same
Abi: 0b4e43edb4125db815f327a6fac48656ee7bb7c9924c403b4f9321c812e979da
Description: Audio playback and capture library written in C, in a single source file
Status: install ok installed

Package: libogg
Version: 1.3.6
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: 3f72593abca64431ffc805648b7884ba778db547ead3337e99fb857e453fda5d
Description: Ogg is a multimedia container format, and the native file and stream format for the Xiph.org multimedia codecs.
Status: install ok installed

Package: libvorbis
Version: 1.3.7
Port-Version: 4
Depends: libogg, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: 22574fa72552c32cd163938282defa29f05091079122aacfd621bbe4a64b7ab8
Description: Ogg Vorbis is a fully open, non-proprietary, patent-and-royalty-free, general-purpose compressed audio format
Status: install ok installed

Package: libflac
Version: 1.5.0
Depends: libogg, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: 47ccc5a5343f7cf77735c5f0f2cf854c5f06bb1009eb972a35aab833f8d79a99
Description: Library for manipulating FLAC files
Default-Features: stack-protector
Status: install ok installed

Package: libflac
Feature: stack-protector
Architecture: x64-linux
Multi-Arch: same
Description: Build with stack smashing protection
Status: install ok installed

Package: sfml
Version: 3.0.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-linux
Multi-Arch: same
Abi: c0d68b6b70660bc0acb9492daf538d415ca86f9dd5d9c5fc48fcbcdfc5908299
Description: Simple and fast multimedia library
Default-Features: audio, graphics, network, window
Status: install ok installed

Package: sfml
Feature: audio
Depends: libflac, libogg, libvorbis, miniaudio
Architecture: x64-linux
Multi-Arch: same
Description: Use sfml-audio library
Status: install ok installed

Package: sfml
Feature: graphics
Depends: freetype, sfml, stb
Architecture: x64-linux
Multi-Arch: same
Description: Use sfml-graphics library
Status: install ok installed

Package: sfml
Feature: network
Architecture: x64-linux
Multi-Arch: same
Description: Use sfml-network library
Status: install ok installed

Package: sfml
Feature: window
Depends: opengl
Architecture: x64-linux
Multi-Arch: same
Description: Use sfml-window library
Status: install ok installed

