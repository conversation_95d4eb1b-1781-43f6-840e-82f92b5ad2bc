x64-linux/
x64-linux/debug/
x64-linux/debug/lib/
x64-linux/debug/lib/libfreetyped.a
x64-linux/debug/lib/pkgconfig/
x64-linux/debug/lib/pkgconfig/freetype2.pc
x64-linux/include/
x64-linux/include/freetype/
x64-linux/include/freetype/config/
x64-linux/include/freetype/config/ftconfig.h
x64-linux/include/freetype/config/ftheader.h
x64-linux/include/freetype/config/ftmodule.h
x64-linux/include/freetype/config/ftoption.h
x64-linux/include/freetype/config/ftstdlib.h
x64-linux/include/freetype/config/integer-types.h
x64-linux/include/freetype/config/mac-support.h
x64-linux/include/freetype/config/public-macros.h
x64-linux/include/freetype/freetype.h
x64-linux/include/freetype/ftadvanc.h
x64-linux/include/freetype/ftbbox.h
x64-linux/include/freetype/ftbdf.h
x64-linux/include/freetype/ftbitmap.h
x64-linux/include/freetype/ftbzip2.h
x64-linux/include/freetype/ftcache.h
x64-linux/include/freetype/ftchapters.h
x64-linux/include/freetype/ftcid.h
x64-linux/include/freetype/ftcolor.h
x64-linux/include/freetype/ftdriver.h
x64-linux/include/freetype/fterrdef.h
x64-linux/include/freetype/fterrors.h
x64-linux/include/freetype/ftfntfmt.h
x64-linux/include/freetype/ftgasp.h
x64-linux/include/freetype/ftglyph.h
x64-linux/include/freetype/ftgxval.h
x64-linux/include/freetype/ftgzip.h
x64-linux/include/freetype/ftimage.h
x64-linux/include/freetype/ftincrem.h
x64-linux/include/freetype/ftlcdfil.h
x64-linux/include/freetype/ftlist.h
x64-linux/include/freetype/ftlogging.h
x64-linux/include/freetype/ftlzw.h
x64-linux/include/freetype/ftmac.h
x64-linux/include/freetype/ftmm.h
x64-linux/include/freetype/ftmodapi.h
x64-linux/include/freetype/ftmoderr.h
x64-linux/include/freetype/ftotval.h
x64-linux/include/freetype/ftoutln.h
x64-linux/include/freetype/ftparams.h
x64-linux/include/freetype/ftpfr.h
x64-linux/include/freetype/ftrender.h
x64-linux/include/freetype/ftsizes.h
x64-linux/include/freetype/ftsnames.h
x64-linux/include/freetype/ftstroke.h
x64-linux/include/freetype/ftsynth.h
x64-linux/include/freetype/ftsystem.h
x64-linux/include/freetype/fttrigon.h
x64-linux/include/freetype/fttypes.h
x64-linux/include/freetype/ftwinfnt.h
x64-linux/include/freetype/otsvg.h
x64-linux/include/freetype/t1tables.h
x64-linux/include/freetype/ttnameid.h
x64-linux/include/freetype/tttables.h
x64-linux/include/freetype/tttags.h
x64-linux/include/ft2build.h
x64-linux/lib/
x64-linux/lib/libfreetype.a
x64-linux/lib/pkgconfig/
x64-linux/lib/pkgconfig/freetype2.pc
x64-linux/share/
x64-linux/share/freetype/
x64-linux/share/freetype/copyright
x64-linux/share/freetype/freetype-config-version.cmake
x64-linux/share/freetype/freetype-config.cmake
x64-linux/share/freetype/freetype-targets-debug.cmake
x64-linux/share/freetype/freetype-targets-release.cmake
x64-linux/share/freetype/freetype-targets.cmake
x64-linux/share/freetype/usage
x64-linux/share/freetype/vcpkg-cmake-wrapper.cmake
x64-linux/share/freetype/vcpkg.spdx.json
x64-linux/share/freetype/vcpkg_abi_info.txt
