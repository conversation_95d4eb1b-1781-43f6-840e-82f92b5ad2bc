x64-linux/
x64-linux/debug/
x64-linux/debug/lib/
x64-linux/debug/lib/libsfml-audio-s-d.a
x64-linux/debug/lib/libsfml-graphics-s-d.a
x64-linux/debug/lib/libsfml-network-s-d.a
x64-linux/debug/lib/libsfml-system-s-d.a
x64-linux/debug/lib/libsfml-window-s-d.a
x64-linux/debug/lib/pkgconfig/
x64-linux/debug/lib/pkgconfig/sfml-all.pc
x64-linux/debug/lib/pkgconfig/sfml-audio.pc
x64-linux/debug/lib/pkgconfig/sfml-graphics.pc
x64-linux/debug/lib/pkgconfig/sfml-network.pc
x64-linux/debug/lib/pkgconfig/sfml-system.pc
x64-linux/debug/lib/pkgconfig/sfml-window.pc
x64-linux/include/
x64-linux/include/SFML/
x64-linux/include/SFML/Audio.hpp
x64-linux/include/SFML/Audio/
x64-linux/include/SFML/Audio/AudioResource.hpp
x64-linux/include/SFML/Audio/Export.hpp
x64-linux/include/SFML/Audio/InputSoundFile.hpp
x64-linux/include/SFML/Audio/Listener.hpp
x64-linux/include/SFML/Audio/Music.hpp
x64-linux/include/SFML/Audio/OutputSoundFile.hpp
x64-linux/include/SFML/Audio/PlaybackDevice.hpp
x64-linux/include/SFML/Audio/Sound.hpp
x64-linux/include/SFML/Audio/SoundBuffer.hpp
x64-linux/include/SFML/Audio/SoundBufferRecorder.hpp
x64-linux/include/SFML/Audio/SoundChannel.hpp
x64-linux/include/SFML/Audio/SoundFileFactory.hpp
x64-linux/include/SFML/Audio/SoundFileFactory.inl
x64-linux/include/SFML/Audio/SoundFileReader.hpp
x64-linux/include/SFML/Audio/SoundFileWriter.hpp
x64-linux/include/SFML/Audio/SoundRecorder.hpp
x64-linux/include/SFML/Audio/SoundSource.hpp
x64-linux/include/SFML/Audio/SoundStream.hpp
x64-linux/include/SFML/Config.hpp
x64-linux/include/SFML/GpuPreference.hpp
x64-linux/include/SFML/Graphics.hpp
x64-linux/include/SFML/Graphics/
x64-linux/include/SFML/Graphics/BlendMode.hpp
x64-linux/include/SFML/Graphics/CircleShape.hpp
x64-linux/include/SFML/Graphics/Color.hpp
x64-linux/include/SFML/Graphics/Color.inl
x64-linux/include/SFML/Graphics/ConvexShape.hpp
x64-linux/include/SFML/Graphics/CoordinateType.hpp
x64-linux/include/SFML/Graphics/Drawable.hpp
x64-linux/include/SFML/Graphics/Export.hpp
x64-linux/include/SFML/Graphics/Font.hpp
x64-linux/include/SFML/Graphics/Glsl.hpp
x64-linux/include/SFML/Graphics/Glsl.inl
x64-linux/include/SFML/Graphics/Glyph.hpp
x64-linux/include/SFML/Graphics/Image.hpp
x64-linux/include/SFML/Graphics/PrimitiveType.hpp
x64-linux/include/SFML/Graphics/Rect.hpp
x64-linux/include/SFML/Graphics/Rect.inl
x64-linux/include/SFML/Graphics/RectangleShape.hpp
x64-linux/include/SFML/Graphics/RenderStates.hpp
x64-linux/include/SFML/Graphics/RenderTarget.hpp
x64-linux/include/SFML/Graphics/RenderTexture.hpp
x64-linux/include/SFML/Graphics/RenderWindow.hpp
x64-linux/include/SFML/Graphics/Shader.hpp
x64-linux/include/SFML/Graphics/Shape.hpp
x64-linux/include/SFML/Graphics/Sprite.hpp
x64-linux/include/SFML/Graphics/StencilMode.hpp
x64-linux/include/SFML/Graphics/Text.hpp
x64-linux/include/SFML/Graphics/Texture.hpp
x64-linux/include/SFML/Graphics/Transform.hpp
x64-linux/include/SFML/Graphics/Transform.inl
x64-linux/include/SFML/Graphics/Transformable.hpp
x64-linux/include/SFML/Graphics/Vertex.hpp
x64-linux/include/SFML/Graphics/VertexArray.hpp
x64-linux/include/SFML/Graphics/VertexBuffer.hpp
x64-linux/include/SFML/Graphics/View.hpp
x64-linux/include/SFML/Main.hpp
x64-linux/include/SFML/Network.hpp
x64-linux/include/SFML/Network/
x64-linux/include/SFML/Network/Export.hpp
x64-linux/include/SFML/Network/Ftp.hpp
x64-linux/include/SFML/Network/Http.hpp
x64-linux/include/SFML/Network/IpAddress.hpp
x64-linux/include/SFML/Network/Packet.hpp
x64-linux/include/SFML/Network/Socket.hpp
x64-linux/include/SFML/Network/SocketHandle.hpp
x64-linux/include/SFML/Network/SocketSelector.hpp
x64-linux/include/SFML/Network/TcpListener.hpp
x64-linux/include/SFML/Network/TcpSocket.hpp
x64-linux/include/SFML/Network/UdpSocket.hpp
x64-linux/include/SFML/OpenGL.hpp
x64-linux/include/SFML/System.hpp
x64-linux/include/SFML/System/
x64-linux/include/SFML/System/Angle.hpp
x64-linux/include/SFML/System/Angle.inl
x64-linux/include/SFML/System/Clock.hpp
x64-linux/include/SFML/System/Err.hpp
x64-linux/include/SFML/System/Exception.hpp
x64-linux/include/SFML/System/Export.hpp
x64-linux/include/SFML/System/FileInputStream.hpp
x64-linux/include/SFML/System/InputStream.hpp
x64-linux/include/SFML/System/MemoryInputStream.hpp
x64-linux/include/SFML/System/NativeActivity.hpp
x64-linux/include/SFML/System/Sleep.hpp
x64-linux/include/SFML/System/String.hpp
x64-linux/include/SFML/System/String.inl
x64-linux/include/SFML/System/SuspendAwareClock.hpp
x64-linux/include/SFML/System/Time.hpp
x64-linux/include/SFML/System/Time.inl
x64-linux/include/SFML/System/Utf.hpp
x64-linux/include/SFML/System/Utf.inl
x64-linux/include/SFML/System/Vector2.hpp
x64-linux/include/SFML/System/Vector2.inl
x64-linux/include/SFML/System/Vector3.hpp
x64-linux/include/SFML/System/Vector3.inl
x64-linux/include/SFML/Window.hpp
x64-linux/include/SFML/Window/
x64-linux/include/SFML/Window/Clipboard.hpp
x64-linux/include/SFML/Window/Context.hpp
x64-linux/include/SFML/Window/ContextSettings.hpp
x64-linux/include/SFML/Window/Cursor.hpp
x64-linux/include/SFML/Window/Event.hpp
x64-linux/include/SFML/Window/Event.inl
x64-linux/include/SFML/Window/Export.hpp
x64-linux/include/SFML/Window/GlResource.hpp
x64-linux/include/SFML/Window/Joystick.hpp
x64-linux/include/SFML/Window/Keyboard.hpp
x64-linux/include/SFML/Window/Mouse.hpp
x64-linux/include/SFML/Window/Sensor.hpp
x64-linux/include/SFML/Window/Touch.hpp
x64-linux/include/SFML/Window/VideoMode.hpp
x64-linux/include/SFML/Window/Vulkan.hpp
x64-linux/include/SFML/Window/Window.hpp
x64-linux/include/SFML/Window/WindowBase.hpp
x64-linux/include/SFML/Window/WindowBase.inl
x64-linux/include/SFML/Window/WindowEnums.hpp
x64-linux/include/SFML/Window/WindowHandle.hpp
x64-linux/lib/
x64-linux/lib/libsfml-audio-s.a
x64-linux/lib/libsfml-graphics-s.a
x64-linux/lib/libsfml-network-s.a
x64-linux/lib/libsfml-system-s.a
x64-linux/lib/libsfml-window-s.a
x64-linux/lib/pkgconfig/
x64-linux/lib/pkgconfig/sfml-all.pc
x64-linux/lib/pkgconfig/sfml-audio.pc
x64-linux/lib/pkgconfig/sfml-graphics.pc
x64-linux/lib/pkgconfig/sfml-network.pc
x64-linux/lib/pkgconfig/sfml-system.pc
x64-linux/lib/pkgconfig/sfml-window.pc
x64-linux/share/
x64-linux/share/doc/
x64-linux/share/doc/SFML/
x64-linux/share/doc/SFML/license.md
x64-linux/share/doc/SFML/readme.md
x64-linux/share/sfml/
x64-linux/share/sfml/FindDRM.cmake
x64-linux/share/sfml/FindEGL.cmake
x64-linux/share/sfml/FindFLAC.cmake
x64-linux/share/sfml/FindFreetype.cmake
x64-linux/share/sfml/FindGBM.cmake
x64-linux/share/sfml/FindGLES.cmake
x64-linux/share/sfml/FindUDev.cmake
x64-linux/share/sfml/FindVorbis.cmake
x64-linux/share/sfml/SFMLAudioDependencies.cmake
x64-linux/share/sfml/SFMLAudioStaticTargets-debug.cmake
x64-linux/share/sfml/SFMLAudioStaticTargets-release.cmake
x64-linux/share/sfml/SFMLAudioStaticTargets.cmake
x64-linux/share/sfml/SFMLConfig.cmake
x64-linux/share/sfml/SFMLConfigVersion.cmake
x64-linux/share/sfml/SFMLGraphicsDependencies.cmake
x64-linux/share/sfml/SFMLGraphicsStaticTargets-debug.cmake
x64-linux/share/sfml/SFMLGraphicsStaticTargets-release.cmake
x64-linux/share/sfml/SFMLGraphicsStaticTargets.cmake
x64-linux/share/sfml/SFMLNetworkStaticTargets-debug.cmake
x64-linux/share/sfml/SFMLNetworkStaticTargets-release.cmake
x64-linux/share/sfml/SFMLNetworkStaticTargets.cmake
x64-linux/share/sfml/SFMLSystemDependencies.cmake
x64-linux/share/sfml/SFMLSystemStaticTargets-debug.cmake
x64-linux/share/sfml/SFMLSystemStaticTargets-release.cmake
x64-linux/share/sfml/SFMLSystemStaticTargets.cmake
x64-linux/share/sfml/SFMLWindowDependencies.cmake
x64-linux/share/sfml/SFMLWindowStaticTargets-debug.cmake
x64-linux/share/sfml/SFMLWindowStaticTargets-release.cmake
x64-linux/share/sfml/SFMLWindowStaticTargets.cmake
x64-linux/share/sfml/copyright
x64-linux/share/sfml/usage
x64-linux/share/sfml/vcpkg.spdx.json
x64-linux/share/sfml/vcpkg_abi_info.txt
