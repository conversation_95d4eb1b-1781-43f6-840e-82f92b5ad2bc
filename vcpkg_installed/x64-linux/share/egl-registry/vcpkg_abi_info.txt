cmake 4.0.3-dirty
copyright 5d77554ffc974ce346430a01a30be8176ca5c46fb1b53a7233291195ded592d3
features core
portfile.cmake 1b3c1caef5c9f17a3210bb1452c1de64c79b7acf61dc8758250d27626a5ae5fe
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
vcpkg.json f207cbb5f7ab4de2a41a020e0d503eaad4efc1afe5440bbbcf4689e657b57424
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
