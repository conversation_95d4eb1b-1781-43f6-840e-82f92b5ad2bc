{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/catch2-x64-linux-3.9.0-d712920e-832d-4ada-9176-9c280747c0fc", "name": "catch2:x64-linux@3.9.0 b0a8918b9c69ee35c8bd0d33e9bd816f6cffdb3d690f657ef184e937485061a6", "creationInfo": {"creators": ["Tool: vcpkg-2025-07-21-d4b65a2b83ae6c3526acd1c6f3b51aff2a884533"], "created": "2025-08-08T09:53:57Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "catch2", "SPDXID": "SPDXRef-port", "versionInfo": "3.9.0", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/catch2", "homepage": "https://github.com/catchorg/Catch2", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A modern, C++-native, test framework for unit-tests, TDD and BDD.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "catch2:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "b0a8918b9c69ee35c8bd0d33e9bd816f6cffdb3d690f657ef184e937485061a6", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "catchorg/Catch2", "downloadLocation": "git+https://github.com/catchorg/Catch2@v3.9.0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "b215216088dbebf0d590351767e71e7643158b26835e3d6dab8c826d8252c4ab09697dd9eaf37d34ad7fc7cc367846bf501ee9489ba3a407ace00ae5d18268ed"}]}], "files": [{"fileName": "./fix-install-path.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "0f31b76a1d41d1e1917d18cf8c757fb7bdc4007a6ef2d60075ca7f267614d677"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "de2500a7a0ec8d332175f1095d663c3742c567df3d95d0fb9c3cc1b3de55084b"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "0062efe05684c561cc0b798b4309de3bce394765b5f36dfdbe85da40e9d6bdf4"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}