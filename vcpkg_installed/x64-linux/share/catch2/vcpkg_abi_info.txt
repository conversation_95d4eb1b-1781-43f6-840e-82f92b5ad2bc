cmake 4.0.3-dirty
features core
fix-install-path.patch 0f31b76a1d41d1e1917d18cf8c757fb7bdc4007a6ef2d60075ca7f267614d677
portfile.cmake de2500a7a0ec8d332175f1095d663c3742c567df3d95d0fb9c3cc1b3de55084b
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
vcpkg-cmake 764969545aa87c5bf5bbbbe075296e2cf6b505a45c1111c6384a3c32d9181a8f
vcpkg-cmake-config 4964c99aba9675e96fcf2e0da2973632879863d9a42b047b92d248df0fdca760
vcpkg.json 0062efe05684c561cc0b798b4309de3bce394765b5f36dfdbe85da40e9d6bdf4
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_check_linkage f8d5dc4ee94493155b76fb825a74e9c4f6037569b990c3f8c02074338223fbbd
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
