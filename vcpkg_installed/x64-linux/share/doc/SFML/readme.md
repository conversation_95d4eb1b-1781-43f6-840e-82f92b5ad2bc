[![SFML logo](https://www.sfml-dev.org/images/logo.png)](https://www.sfml-dev.org)

# SFML — Simple and Fast Multimedia Library

SFML is a simple, fast, cross-platform and object-oriented multimedia API. It provides access to windowing, graphics, audio and network. It is written in C++, and has bindings for various languages such as C, .Net, Ruby, Python.

## State of Development

Development is focused on version 3 in the `master` branch.
No more features are planned for the 2.x release series.

## CMake Template

The easiest way to get started with SFML is our [CMake-based project template](https://github.com/SFML/cmake-sfml-project/blob/master/README.md).
This template will automatically download and build SFML alongside your own application.
Read the README for full instructions on how to use it.

## Download

-   You can get the latest official release on [SFML's website](https://www.sfml-dev.org/download.php).
-   You can also get the source code of the current development version from the [Git repository](https://github.com/SFML/SFML).
-   Alternatively, you can get the latest snapshot / artifact builds from the [artifacts storage](https://artifacts.sfml-dev.org/by-branch/master/).

## Install

Follow the instructions of the [tutorials](https://www.sfml-dev.org/tutorials/), there is one for each platform/compiler that SFML supports.

## Learn

There are several places to learn SFML:

-   The [official tutorials](https://www.sfml-dev.org/tutorials/)
-   The [online API documentation](https://www.sfml-dev.org/documentation/)
-   The [community wiki](https://github.com/SFML/SFML/wiki/)

## Community

Here are some useful community links:

-   [Discord](https://discord.gg/nr4X7Fh)
-   [Twitter](https://twitter.com/sfmldev)
-   [Forum](https://en.sfml-dev.org/forums/) ([French](https://fr.sfml-dev.org/forums/))

## Contribute

SFML is an open-source project, and it needs your help to go on growing and improving. If you want to get involved and suggest some additional features, file a bug report or submit a patch, please have a look at the [contribution guidelines](https://www.sfml-dev.org/contribute.php).

## Authors

-   Laurent Gomila (<EMAIL>)
-   Marco Antognini (<EMAIL>)
-   binary1248 (<EMAIL>)
-   Lukas Dürrenberger (<EMAIL>)
-   Jonathan De Wachter (<EMAIL>)
-   Jan Haller (<EMAIL>)
-   Mario Liebisch (<EMAIL>)
-   Stefan Schindler (<EMAIL>)
-   Artur Moreira (<EMAIL>)
-   Vittorio Romeo (<EMAIL>)
-   Chris Thrasher (<EMAIL>)
-   And many other members of the SFML community

## License

The SFML libraries and source code are distributed under the [zlib/libpng license](https://opensource.org/licenses/Zlib). See [license.md](license.md). External libraries used by SFML are distributed under their own licenses.

In short, SFML is free for any use (commercial or personal, proprietary or open-source). You can use SFML in your project without any restriction. You can even omit to mention that you use SFML -- although it would be appreciated.

## External libraries used by SFML

-   [_stb_image_ and _stb_image_write_](https://github.com/nothings/stb) are [public domain](https://github.com/nothings/stb/blob/master/LICENSE)
-   [_freetype_](https://gitlab.freedesktop.org/freetype/freetype) is under the [FreeType license or the GPL license](https://gitlab.freedesktop.org/freetype/freetype/-/blob/master/LICENSE.TXT)
-   [_libogg_](https://gitlab.xiph.org/xiph/ogg) is under the [BSD license](https://gitlab.xiph.org/xiph/ogg/-/blob/master/COPYING)
-   [_libvorbis_](https://gitlab.xiph.org/xiph/vorbis) is under the [BSD license](https://gitlab.xiph.org/xiph/vorbis/-/blob/master/COPYING)
-   [_libflac_](https://gitlab.xiph.org/xiph/flac) is under the [BSD license](https://gitlab.xiph.org/xiph/flac/-/blob/master/COPYING.Xiph)
-   [_minimp3_](https://github.com/lieff/minimp3) is under the [CC0 license](https://github.com/lieff/minimp3/blob/master/LICENSE)
-   [_miniaudio_](https://github.com/mackron/miniaudio) is [public domain or under the MIT No Attribution license](https://github.com/mackron/miniaudio/blob/master/LICENSE)
