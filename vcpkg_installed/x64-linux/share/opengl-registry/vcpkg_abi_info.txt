cmake 4.0.3-dirty
copyright c56ffb0cc40bd972c1b2d4759828b89f7860471f5ba52eedde7a4ddb4664cef5
egl-registry 129ac93350da7f8ceca6e07a521ce1594d43875c8e147d033e284812be4adf44
features core
portfile.cmake 138eebe924b8f930902ab6b32349b52d3f5188034b20d5e12786e99bd412c4b5
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
vcpkg.json 61709425e144b4bb228460483edbb170a59b350795d79d7730096ba1b55f3a1c
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
