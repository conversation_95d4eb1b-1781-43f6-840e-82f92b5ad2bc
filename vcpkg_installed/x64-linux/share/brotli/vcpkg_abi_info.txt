cmake 4.0.3-dirty
emscripten.patch 416ffaeb778323dd210a34b93d02efc2820b794c98c28f0ac5f5d82799cb50fd
features core
fix-arm-uwp.patch c9038ac61297ac59aa34bf0c93e3f2cea9d5f053e65ce5e4ad0207db47594720
install.patch 2945ebec5e350e275e1c0076a2b5bd4bd7bae4a9c6f1cef2a19988b739f4b75e
pkgconfig.patch a678ab71351fbf8ed2b80ca3c6934a332f74af9c6c0ee6d7ea1b0a598b8c8d08
portfile.cmake f95cbfbd877b11da6e6dd4411c7d9744a8c7ea99cc77e9c967c4d9418a265310
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
usage 2fc37651df1d64d9134a1aa6597de5f927efe1b5138a243bb87ba746aca04416
vcpkg-cmake 764969545aa87c5bf5bbbbe075296e2cf6b505a45c1111c6384a3c32d9181a8f
vcpkg-cmake-config 4964c99aba9675e96fcf2e0da2973632879863d9a42b047b92d248df0fdca760
vcpkg.json 98b60e394a4b54e250ddf315d319ff17bae4057c7ab7bf14790e57e437731417
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
