01-fix-dependency-resolve.patch 06084daa53e0bc531158087d3a64d4c101078a2d92413af0fe8723c8361ba0dc
02-fix-pkgconfig.patch f6574011a87a607b90ee00b7392032f318c38023ea099b0901533091f7cbaac5
03-fix-android-install-path.patch 1b148dca9a5ca36b19b433860bc5f07f1faab0f9fa21fddef90e58dac4c94373
cmake 4.0.3-dirty
features audio;core;graphics;network;window
freetype 3d8c47338df2a3359a431dd9dbb1fdf17ea9f71d84812a67f1c718baaaf4ecfe
libflac 47ccc5a5343f7cf77735c5f0f2cf854c5f06bb1009eb972a35aab833f8d79a99
libogg 3f72593abca64431ffc805648b7884ba778db547ead3337e99fb857e453fda5d
libvorbis 22574fa72552c32cd163938282defa29f05091079122aacfd621bbe4a64b7ab8
miniaudio 0b4e43edb4125db815f327a6fac48656ee7bb7c9924c403b4f9321c812e979da
opengl e596ad348042b84b0380dcfb60e3602dc06fc724904ee9215eabc294f6256781
portfile.cmake 176215ec65524591fdf7f2f2ed9df80a2fa1af6569f093f471610acb2660103f
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
stb f1dab91e8709e3b8729e65046151d931c03c7b55f56186fc420f99eba176cd75
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
usage f77eb134a36bd8c8a384966e72fb84dd3c8ecd1242adc58cf9e0261b374891af
vcpkg-cmake 764969545aa87c5bf5bbbbe075296e2cf6b505a45c1111c6384a3c32d9181a8f
vcpkg-cmake-config 4964c99aba9675e96fcf2e0da2973632879863d9a42b047b92d248df0fdca760
vcpkg.json 2a756d06d85d68d120886c14fd6598573ed1408916d749767ccd7f125a991f7d
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
