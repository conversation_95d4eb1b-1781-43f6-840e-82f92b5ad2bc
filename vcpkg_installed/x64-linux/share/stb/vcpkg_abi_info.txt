FindStb.cmake d6d9e778ddb6e7b5d13c63a63872a7a5ddba00b1cef675a7cc7c91a9a9508d91
cmake 4.0.3-dirty
features core
portfile.cmake 614c4f6b0981b6010a6140f37c7943f4d89663f24044dccfcbf5f5aabacd0fb9
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
usage cf896a6d0956431ce01388ad6f62f7f7aa3d9c5e0d23f7da7f9ee3568c61f78a
vcpkg-cmake-wrapper.cmake 7580cacc3272d0ccedc17f9303524ec1a11c43f0476318fe840816478b8def40
vcpkg.json c542cb38e83845fbbb34988e016d1098fe50ee7a8a5e88c6b6a1efab4b9868d5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
