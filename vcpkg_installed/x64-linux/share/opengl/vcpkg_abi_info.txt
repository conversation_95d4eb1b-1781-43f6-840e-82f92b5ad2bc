cmake 4.0.3-dirty
features core
glu.pc.in c1225cb320b7e2dfc19a5f81013f420caae0ee0174c28fefea5136a676de7a38
opengl-registry 76747cb9051ae8ea9d1c00318997f36e7badad48b3f2f2853f80e1cc1bb4c137
opengl.pc.in 72d9d55d55ca7ee852fd9e8473295b8faf34e69d6c2e7b01230c17e68a3f75b7
portfile.cmake be1ddde13da8627b55b39b1a94aa14d69c87e191fadc1134cc2e9011a1ba6032
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
triplet x64-linux
triplet_abi 3600011444071beb27b81ff82b3c866e3d4054636e3011609deb313d2baf57fd-18a85c56848e0038a9e90eae6d40ce71969e28dc105e87ebf609fa2dbc195c51-7fdd04be2c789fa12a25031f7c6daa49ad3faf5e
usage e89a9b1dc4f4af7ff9c5f91af4f6d8a0165958be547fbcd1b1a57e4765f5b13a
vcpkg.json 220f239373c77aa668fb692ce1191013cb57d8c38881dfe154f74e14215e8c0f
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_get_windows_sdk 1bb57ecf48db9703b9496a40ff9eeec83b856362b426d23e7d25a54a0ac55916
