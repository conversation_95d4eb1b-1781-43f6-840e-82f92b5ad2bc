cmake_policy(PUSH)
cmake_policy(SET CMP0012 NEW)
cmake_policy(SET CMP0054 NEW)

list(REMOVE_ITEM ARGS "NO_MODULE" "CONFIG" "MODULE")
_find_package(${ARGS} CONFIG)

if(Freetype_FOUND)
    include("${CMAKE_ROOT}/Modules/SelectLibraryConfigurations.cmake")

    get_target_property(_freetype_include_dirs freetype INTERFACE_INCLUDE_DIRECTORIES)

    if (CMAKE_SYSTEM_NAME STREQUAL "Windows" OR CMAKE_SYSTEM_NAME STREQUAL "WindowsStore")
        get_target_property(_freetype_location_debug freetype IMPORTED_IMPLIB_DEBUG)
        get_target_property(_freetype_location_release freetype IMPORTED_IMPLIB_RELEASE)
    endif()
    if(NOT _freetype_location_debug AND NOT _freetype_location_release)
        get_target_property(_freetype_location_debug freetype IMPORTED_LOCATION_DEBUG)
        get_target_property(_freetype_location_release freetype IMPORTED_LOCATION_RELEASE)
    endif()

    set(FREETYPE_FOUND TRUE)

    set(FREETYPE_INCLUDE_DIRS "${_freetype_include_dirs}")
    set(FREETYPE_INCLUDE_DIR_ft2build "${_freetype_include_dirs}")
    set(FREETYPE_INCLUDE_DIR_freetype2 "${_freetype_include_dirs}")
    set(FREETYPE_LIBRARY_DEBUG "${_freetype_location_debug}" CACHE INTERNAL "vcpkg")
    set(FREETYPE_LIBRARY_RELEASE "${_freetype_location_release}" CACHE INTERNAL "vcpkg")
    select_library_configurations(FREETYPE)
    set(FREETYPE_LIBRARIES ${FREETYPE_LIBRARY})
    set(FREETYPE_VERSION_STRING "${Freetype_VERSION}")

    unset(_freetype_include_dirs)
    unset(_freetype_location_debug)
    unset(_freetype_location_release)
endif()

if("static" STREQUAL "static")
    if("ON")
        find_package(ZLIB)
    endif()
    if("ON")
        find_package(BZip2)
    endif()
    if("ON")
        find_package(PNG)
    endif()
    if("ON")
        find_library(BROTLIDEC_LIBRARY_RELEASE NAMES brotlidec brotlidec-static PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}" PATH_SUFFIXES lib NO_DEFAULT_PATH)
        find_library(BROTLIDEC_LIBRARY_DEBUG NAMES brotlidec brotlidec-static brotlidecd brotlidec-staticd PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug" PATH_SUFFIXES lib NO_DEFAULT_PATH)
        find_library(BROTLICOMMON_LIBRARY_RELEASE NAMES brotlicommon brotlicommon-static PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}" PATH_SUFFIXES lib NO_DEFAULT_PATH)
        find_library(BROTLICOMMON_LIBRARY_DEBUG NAMES brotlicommon brotlicommon-static brotlicommond brotlicommon-staticd PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug" PATH_SUFFIXES lib NO_DEFAULT_PATH)
        include(SelectLibraryConfigurations)
        select_library_configurations(BROTLIDEC)
        select_library_configurations(BROTLICOMMON)
    endif("ON")

    if(TARGET Freetype::Freetype)
        if("ON")
            set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES ZLIB::ZLIB)
        endif()
        if("ON")
            set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES BZip2::BZip2)
        endif()
        if("ON")
            set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES PNG::PNG)
        endif()
        if("ON")
            if(BROTLIDEC_LIBRARY_DEBUG)
                set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES "\$<\$<CONFIG:DEBUG>:${BROTLIDEC_LIBRARY_DEBUG}>")
                set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES "\$<\$<CONFIG:DEBUG>:${BROTLICOMMON_LIBRARY_DEBUG}>")
            endif()
            if(BROTLIDEC_LIBRARY_RELEASE)
                set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES "\$<\$<NOT:$<CONFIG:DEBUG>>:${BROTLIDEC_LIBRARY_RELEASE}>")
                set_property(TARGET Freetype::Freetype APPEND PROPERTY INTERFACE_LINK_LIBRARIES "\$<\$<NOT:$<CONFIG:DEBUG>>:${BROTLICOMMON_LIBRARY_RELEASE}>")
            endif()
        endif()
    endif()

    if(FREETYPE_LIBRARIES)
        if("ON")
            list(APPEND FREETYPE_LIBRARIES ${ZLIB_LIBRARIES})
        endif()
        if("ON")
            list(APPEND FREETYPE_LIBRARIES ${BZIP2_LIBRARIES})
        endif()
        if("ON")
            list(APPEND FREETYPE_LIBRARIES ${PNG_LIBRARIES})
        endif()
        if("ON")
            list(APPEND FREETYPE_LIBRARIES ${BROTLIDEC_LIBRARIES} ${BROTLICOMMON_LIBRARIES})
        endif()
    endif()
endif()
cmake_policy(POP)
