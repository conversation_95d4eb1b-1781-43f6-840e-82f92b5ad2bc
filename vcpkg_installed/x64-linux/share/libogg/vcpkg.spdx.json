{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/libogg-x64-linux-1.3.6-66a61b06-577a-4f3f-9922-215a1e9d8499", "name": "libogg:x64-linux@1.3.6 3f72593abca64431ffc805648b7884ba778db547ead3337e99fb857e453fda5d", "creationInfo": {"creators": ["Tool: vcpkg-2025-07-21-d4b65a2b83ae6c3526acd1c6f3b51aff2a884533"], "created": "2025-08-08T09:22:34Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "libogg", "SPDXID": "SPDXRef-port", "versionInfo": "1.3.6", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/libogg", "homepage": "https://www.xiph.org/ogg", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Ogg is a multimedia container format, and the native file and stream format for the Xiph.org multimedia codecs.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "libogg:x64-linux", "SPDXID": "SPDXRef-binary", "versionInfo": "3f72593abca64431ffc805648b7884ba778db547ead3337e99fb857e453fda5d", "downloadLocation": "NONE", "licenseConcluded": "BSD-3-<PERSON><PERSON>", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "xiph/ogg", "downloadLocation": "git+https://gitlab.xiph.org/xiph/ogg@v1.3.6", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "c41b71a0fcedd97251d01e1a61fd94f6185b2ab0fba96c38e878d354b488b0e9a9a9782674bc06f08c281681c3ddd775be704685d3f6a65131096caa46f261ba"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "29ea9efa836ae08f10f2f294b1c22ef43a39e22e02c6d25122c2c68c5aafbd48"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "f94ac9d88022860613d8606089aa22d76535b2d7ed2cbca0d5c447b5c7ee35c0"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}