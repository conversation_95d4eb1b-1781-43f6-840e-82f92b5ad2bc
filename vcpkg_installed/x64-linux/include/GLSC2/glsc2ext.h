#ifndef __glsc2_glsc2ext_h_
#define __glsc2_glsc2ext_h_ 1

#ifdef __cplusplus
extern "C" {
#endif

/*
** Copyright 2013-2020 The Khronos Group Inc.
** SPDX-License-Identifier: MIT
**
** This header is generated from the Khronos OpenGL / OpenGL ES XML
** API Registry. The current version of the Registry, generator scripts
** used to make the header, and the header can be found at
**   https://github.com/KhronosGroup/OpenGL-Registry
*/

#ifndef GL_APIENTRYP
#define GL_APIENTRYP GL_APIENTRY*
#endif

/* Generated on date 20231129 */

/* Generated C header for:
 * API: glsc2
 * Profile: common
 * Versions considered: 2\.[0-9]
 * Versions emitted: _nomatch_^
 * Default extensions included: glsc2
 * Additional extensions included: _nomatch_^
 * Extensions removed: _nomatch_^
 */

#ifndef GL_OES_depth24
#define GL_OES_depth24 1
#define GL_DEPTH_COMPONENT24_OES          0x81A6
#endif /* GL_OES_depth24 */

#ifndef GL_OES_depth32
#define GL_OES_depth32 1
#define GL_DEPTH_COMPONENT32_OES          0x81A7
#endif /* GL_OES_depth32 */

#ifndef GL_OES_rgb8_rgba8
#define GL_OES_rgb8_rgba8 1
#define GL_RGB8_OES                       0x8051
#define GL_RGBA8_OES                      0x8058
#endif /* GL_OES_rgb8_rgba8 */

#ifndef GL_OES_standard_derivatives
#define GL_OES_standard_derivatives 1
#define GL_FRAGMENT_SHADER_DERIVATIVE_HINT_OES 0x8B8B
#endif /* GL_OES_standard_derivatives */

#ifndef GL_EXT_texture_compression_s3tc
#define GL_EXT_texture_compression_s3tc 1
#define GL_COMPRESSED_RGB_S3TC_DXT1_EXT   0x83F0
#define GL_COMPRESSED_RGBA_S3TC_DXT1_EXT  0x83F1
#define GL_COMPRESSED_RGBA_S3TC_DXT3_EXT  0x83F2
#define GL_COMPRESSED_RGBA_S3TC_DXT5_EXT  0x83F3
#endif /* GL_EXT_texture_compression_s3tc */

#ifndef GL_IMG_pvric_end_to_end_signature
#define GL_IMG_pvric_end_to_end_signature 1
#define GL_PVRIC_SIGNATURE_MISMATCH_IMG   0x8EA3
#endif /* GL_IMG_pvric_end_to_end_signature */

#ifndef GL_IMG_tile_region_protection
#define GL_IMG_tile_region_protection 1
#define GL_TRP_IMG                        0x8EA0
#define GL_TRP_ERROR_CONTEXT_RESET_IMG    0x8EA1
#define GL_TRP_UNSUPPORTED_CONTEXT_IMG    0x8EA2
#endif /* GL_IMG_tile_region_protection */

#ifdef __cplusplus
}
#endif

#endif
