# =============================================================================
# VCPKG_ROOT Auto-Detection
# =============================================================================

function(_detect_vcpkg_root)
    if(DEFINED VCPKG_ROOT AND NOT VCPKG_ROOT STREQUAL "")
        message(STATUS "Using user-specified VCPKG_ROOT: ${VCPKG_ROOT}")
        return()
    endif()

    # Try environment variable first
    if(DEFINED ENV{VCPKG_ROOT} AND NOT "$ENV{VCPKG_ROOT}" STREQUAL "")
        set(VCPKG_ROOT "$ENV{VCPKG_ROOT}" PARENT_SCOPE)
        message(STATUS "Using VCPKG_ROOT from environment: $ENV{VCPKG_ROOT}")
        return()
    endif()

    # Platform-specific fallback locations
    set(fallback_locations)

    if(CMAKE_HOST_WIN32)
        # Windows common locations
        list(APPEND fallback_locations
            "$ENV{LOCALAPPDATA}/vcpkg"
            "$ENV{USERPROFILE}/vcpkg"
            "C:/vcpkg"
            "C:/tools/vcpkg"
        )
    else()
        # Unix-like systems
        list(APPEND fallback_locations
            "$ENV{HOME}/.local/share/vcpkg"
            "$ENV{HOME}/vcpkg"
            "/usr/local/share/vcpkg"
            "/opt/vcpkg"
        )
    endif()

    # Try each fallback location
    foreach(location ${fallback_locations})
        if(EXISTS "${location}")
            set(VCPKG_ROOT "${location}" PARENT_SCOPE)
            message(STATUS "Auto-detected VCPKG_ROOT: ${location}")
            return()
        endif()
    endforeach()

    message(FATAL_ERROR
        "Could not find vcpkg installation. Please:\n"
        "1. Set VCPKG_ROOT environment variable, or\n"
        "2. Pass -DVCPKG_ROOT=/path/to/vcpkg to CMake, or\n"
        "3. Install vcpkg to one of these locations:\n"
        "   ${fallback_locations}")
endfunction()

_detect_vcpkg_root()

# =============================================================================
# Toolchain File Setup
# =============================================================================

set(VCPKG_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")

# Validate toolchain file exists
if(NOT EXISTS "${VCPKG_TOOLCHAIN_FILE}")
    message(FATAL_ERROR
        "vcpkg toolchain file not found: ${VCPKG_TOOLCHAIN_FILE}\n"
        "Please ensure vcpkg is properly installed at: ${VCPKG_ROOT}")
endif()

# Set up the toolchain if not already set
if(NOT DEFINED CMAKE_TOOLCHAIN_FILE OR CMAKE_TOOLCHAIN_FILE STREQUAL "")
    message(STATUS "Using vcpkg toolchain: ${VCPKG_TOOLCHAIN_FILE}")
    set(CMAKE_TOOLCHAIN_FILE "${VCPKG_TOOLCHAIN_FILE}" CACHE STRING "CMake toolchain file" FORCE)
elseif(NOT CMAKE_TOOLCHAIN_FILE STREQUAL VCPKG_TOOLCHAIN_FILE)
    message(WARNING
        "CMAKE_TOOLCHAIN_FILE is already set to: ${CMAKE_TOOLCHAIN_FILE}\n"
        "Expected vcpkg toolchain: ${VCPKG_TOOLCHAIN_FILE}\n"
        "This may cause issues with vcpkg integration.")
endif()

# Include the actual vcpkg toolchain
include("${VCPKG_TOOLCHAIN_FILE}")
