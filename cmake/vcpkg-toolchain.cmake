# Auto-detect VCPKG_ROOT
function(_detect_vcpkg_root)
    if(DEFINED VCPKG_ROOT AND NOT VCPKG_ROOT STREQUAL "")
        return()
    endif()

    if(DEFINED ENV{VCPKG_ROOT} AND NOT "$ENV{VCPKG_ROOT}" STREQUAL "")
        set(VCPKG_ROOT "$ENV{VCPKG_ROOT}" PARENT_SCOPE)
        return()
    endif()

    # Try common locations
    set(fallback_locations)
    if(CMAKE_HOST_WIN32)
        list(APPEND fallback_locations "$ENV{LOCALAPPDATA}/vcpkg" "$ENV{USERPROFILE}/vcpkg" "C:/vcpkg" "C:/tools/vcpkg")
    else()
        list(APPEND fallback_locations "$ENV{HOME}/.local/share/vcpkg" "$ENV{HOME}/vcpkg" "/usr/local/share/vcpkg" "/opt/vcpkg")
    endif()

    foreach(location ${fallback_locations})
        if(EXISTS "${location}")
            set(VCPKG_ROOT "${location}" PARENT_SCOPE)
            return()
        endif()
    endforeach()

    message(FATAL_ERROR "vcpkg not found. Set VCPKG_ROOT environment variable or pass -DVCPKG_ROOT=/path/to/vcpkg")
endfunction()

_detect_vcpkg_root()

set(VCPKG_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")

if(NOT EXISTS "${VCPKG_TOOLCHAIN_FILE}")
    message(FATAL_ERROR "vcpkg toolchain not found: ${VCPKG_TOOLCHAIN_FILE}")
endif()

if(NOT DEFINED CMAKE_TOOLCHAIN_FILE OR CMAKE_TOOLCHAIN_FILE STREQUAL "")
    set(CMAKE_TOOLCHAIN_FILE "${VCPKG_TOOLCHAIN_FILE}" CACHE STRING "CMake toolchain file" FORCE)
elseif(NOT CMAKE_TOOLCHAIN_FILE STREQUAL VCPKG_TOOLCHAIN_FILE)
    message(WARNING "CMAKE_TOOLCHAIN_FILE already set to: ${CMAKE_TOOLCHAIN_FILE}")
endif()

include("${VCPKG_TOOLCHAIN_FILE}")
