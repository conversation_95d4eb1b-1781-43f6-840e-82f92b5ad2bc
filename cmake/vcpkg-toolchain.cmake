# --- Vcpkg Toolchain Wrapper (pass this as -DCMAKE_TOOLCHAIN_FILE) ---

# Auto-detect VCPKG_ROOT
if(NOT DEFINED VCPKG_ROOT)
    if(DEFINED ENV{VCPKG_ROOT})
        set(VCPKG_ROOT "$ENV{VCPKG_ROOT}")
    elseif(WIN32)
        set(VCPKG_ROOT "$ENV{LOCALAPPDATA}/vcpkg")  # Fallback for Windows
    else()
        set(VCPKG_ROOT "$ENV{HOME}/.local/share/vcpkg")  # Fallback for Linux/macOS
    endif()
endif()

set(VCPKG_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake")

# Point CMake at the official vcpkg toolchain file
if(NOT DEFINED CMAKE_TOOLCHAIN_FILE AND EXISTS "${VCPKG_TOOLCHAIN_FILE}")
    message(STATUS "Using vcpkg toolchain from ${VCPKG_TOOLCHAIN_FILE}")
    set(CMAKE_TOOLCHAIN_FILE "${VCPKG_TOOLCHAIN_FILE}" CACHE STRING "")
endif()
