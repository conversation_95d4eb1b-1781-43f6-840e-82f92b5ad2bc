# =============================================================================
# Configuration Options
# =============================================================================

# Allow users to disable automatic operations
option(VCPKG_AUTO_BOOTSTRAP "Automatically bootstrap vcpkg if not found" ON)
option(VCPKG_AUTO_INSTALL "Automatically install dependencies from vcpkg.json" ON)
option(VCPKG_VERBOSE "Enable verbose vcpkg output" OFF)

# =============================================================================
# Validation and Setup
# =============================================================================

# Validate VCPKG_ROOT is set and exists
if(NOT DEFINED VCPKG_ROOT OR VCPKG_ROOT STREQUAL "")
    message(FATAL_ERROR
        "VCPKG_ROOT is not defined. Please set it to your vcpkg installation directory.\n"
        "Example: cmake -DVCPKG_ROOT=/path/to/vcpkg ..")
endif()

if(NOT EXISTS "${VCPKG_ROOT}")
    message(FATAL_ERROR
        "VCPKG_ROOT directory does not exist: ${VCPKG_ROOT}\n"
        "Please ensure vcpkg is cloned/installed at this location.")
endif()

# =============================================================================
# Target Triplet Auto-Detection
# =============================================================================

function(_vcpkg_detect_target_triplet)
    if(DEFINED VCPKG_TARGET_TRIPLET)
        message(STATUS "Using user-specified VCPKG_TARGET_TRIPLET: ${VCPKG_TARGET_TRIPLET}")
        return()
    endif()

    # Use CMake's built-in platform detection for better accuracy
    if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        if(CMAKE_SIZEOF_VOID_P EQUAL 8)
            set(detected_triplet "x64-windows")
        else()
            set(detected_triplet "x86-windows")
        endif()
    elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
        # More robust Apple Silicon detection
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "^(arm64|aarch64)$" OR
           CMAKE_OSX_ARCHITECTURES MATCHES "arm64")
            set(detected_triplet "arm64-osx")
        else()
            set(detected_triplet "x64-osx")
        endif()
    elseif(CMAKE_SYSTEM_NAME STREQUAL "Linux")
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "^(aarch64|arm64)$")
            set(detected_triplet "arm64-linux")
        elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "^(armv7|arm)$")
            set(detected_triplet "arm-linux")
        else()
            set(detected_triplet "x64-linux")
        endif()
    elseif(CMAKE_SYSTEM_NAME STREQUAL "FreeBSD")
        set(detected_triplet "x64-freebsd")
    else()
        message(WARNING
            "Unknown platform '${CMAKE_SYSTEM_NAME}', defaulting to x64-linux. "
            "You may need to set VCPKG_TARGET_TRIPLET manually.")
        set(detected_triplet "x64-linux")
    endif()

    set(VCPKG_TARGET_TRIPLET "${detected_triplet}" PARENT_SCOPE)
    message(STATUS "Auto-detected VCPKG_TARGET_TRIPLET: ${detected_triplet}")
endfunction()

_vcpkg_detect_target_triplet()

# =============================================================================
# Vcpkg Executable Setup
# =============================================================================

# Set vcpkg executable path with proper extension
if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg.exe")
else()
    set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg")
endif()

# =============================================================================
# Bootstrap Function
# =============================================================================

function(_vcpkg_bootstrap)
    if(NOT VCPKG_AUTO_BOOTSTRAP)
        message(FATAL_ERROR
            "vcpkg executable not found at ${VCPKG_EXECUTABLE} and auto-bootstrap is disabled. "
            "Please bootstrap vcpkg manually or enable VCPKG_AUTO_BOOTSTRAP.")
    endif()

    message(STATUS "vcpkg not found, bootstrapping at ${VCPKG_ROOT}...")

    # Determine bootstrap script
    if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        set(bootstrap_script "${VCPKG_ROOT}/bootstrap-vcpkg.bat")
        set(bootstrap_command "${bootstrap_script}")
    else()
        set(bootstrap_script "${VCPKG_ROOT}/bootstrap-vcpkg.sh")
        set(bootstrap_command "${bootstrap_script}")
    endif()

    # Validate bootstrap script exists
    if(NOT EXISTS "${bootstrap_script}")
        message(FATAL_ERROR
            "Bootstrap script not found: ${bootstrap_script}\n"
            "Please ensure you have a complete vcpkg installation.")
    endif()

    # Execute bootstrap with timeout and proper error handling
    execute_process(
        COMMAND ${bootstrap_command}
        WORKING_DIRECTORY "${VCPKG_ROOT}"
        RESULT_VARIABLE bootstrap_result
        OUTPUT_VARIABLE bootstrap_output
        ERROR_VARIABLE bootstrap_error
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_STRIP_TRAILING_WHITESPACE
    )

    if(NOT bootstrap_result EQUAL 0)
        message(FATAL_ERROR
            "Failed to bootstrap vcpkg (exit code ${bootstrap_result})\n"
            "Output: ${bootstrap_output}\n"
            "Error: ${bootstrap_error}")
    endif()

    # Verify vcpkg executable was created
    if(NOT EXISTS "${VCPKG_EXECUTABLE}")
        message(FATAL_ERROR
            "Bootstrap completed but vcpkg executable still not found at ${VCPKG_EXECUTABLE}")
    endif()

    message(STATUS "vcpkg bootstrapped successfully")
endfunction()

# Bootstrap if needed
if(NOT EXISTS "${VCPKG_EXECUTABLE}")
    _vcpkg_bootstrap()
endif()

# =============================================================================
# Dependency Installation Function
# =============================================================================

function(_vcpkg_install_dependencies)
    if(NOT VCPKG_AUTO_INSTALL)
        message(STATUS "Auto-install disabled, skipping dependency installation")
        return()
    endif()

    set(manifest_file "${CMAKE_SOURCE_DIR}/vcpkg.json")
    if(NOT EXISTS "${manifest_file}")
        message(STATUS "No vcpkg.json manifest found, skipping dependency installation")
        return()
    endif()

    # Create a cache file to track when dependencies were last installed
    set(cache_dir "${CMAKE_BINARY_DIR}/vcpkg-cache")
    set(install_cache "${cache_dir}/install.stamp")

    file(MAKE_DIRECTORY "${cache_dir}")

    # Check if we need to reinstall (manifest newer than cache or cache doesn't exist)
    set(need_install FALSE)
    if(NOT EXISTS "${install_cache}")
        set(need_install TRUE)
        set(reason "no previous installation found")
    else()
        file(TIMESTAMP "${manifest_file}" manifest_time)
        file(TIMESTAMP "${install_cache}" cache_time)
        if(manifest_time IS_NEWER_THAN cache_time)
            set(need_install TRUE)
            set(reason "vcpkg.json is newer than last install")
        endif()
    endif()

    if(NOT need_install)
        message(STATUS "vcpkg dependencies are up to date")
        return()
    endif()

    message(STATUS "Installing vcpkg dependencies (${reason})...")

    # Prepare install command
    set(install_args install --triplet "${VCPKG_TARGET_TRIPLET}")

    # Add verbose flag if requested
    if(VCPKG_VERBOSE)
        list(APPEND install_args --verbose)
    endif()

    # Execute install with proper error handling
    execute_process(
        COMMAND "${VCPKG_EXECUTABLE}" ${install_args}
        WORKING_DIRECTORY "${CMAKE_SOURCE_DIR}"
        RESULT_VARIABLE install_result
        OUTPUT_VARIABLE install_output
        ERROR_VARIABLE install_error
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_STRIP_TRAILING_WHITESPACE
    )

    if(NOT install_result EQUAL 0)
        message(FATAL_ERROR
            "vcpkg install failed (exit code ${install_result})\n"
            "Command: ${VCPKG_EXECUTABLE} ${install_args}\n"
            "Output: ${install_output}\n"
            "Error: ${install_error}")
    endif()

    # Create cache stamp file
    file(WRITE "${install_cache}" "Dependencies installed at: ${CMAKE_CURRENT_LIST_FILE}")

    message(STATUS "vcpkg dependencies installed successfully")
endfunction()

# Install dependencies
_vcpkg_install_dependencies()

# =============================================================================
# Status Summary
# =============================================================================

message(STATUS "vcpkg integration complete:")
message(STATUS "  - Root: ${VCPKG_ROOT}")
message(STATUS "  - Executable: ${VCPKG_EXECUTABLE}")
message(STATUS "  - Target triplet: ${VCPKG_TARGET_TRIPLET}")
message(STATUS "  - Auto-bootstrap: ${VCPKG_AUTO_BOOTSTRAP}")
message(STATUS "  - Auto-install: ${VCPKG_AUTO_INSTALL}")
