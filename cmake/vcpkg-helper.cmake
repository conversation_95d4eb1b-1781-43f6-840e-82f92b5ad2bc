# --- Vcpkg integration helper (auto-bootstrap + auto-install + toolchain setup) ---

# Auto-detect VCPKG_TARGET_TRIPLET if not set
if(NOT DEFINED VCPKG_TARGET_TRIPLET)
    if(WIN32)
        if(CMAKE_SIZEOF_VOID_P EQUAL 8)
            set(VCPKG_TARGET_TRIPLET "x64-windows")
        else()
            set(VCPKG_TARGET_TRIPLET "x86-windows")
        endif()
    elseif(APPLE)
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm64")
            set(VCPKG_TARGET_TRIPLET "arm64-osx")
        else()
            set(VCPKG_TARGET_TRIPLET "x64-osx")
        endif()
    elseif(UNIX)
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
            set(VCPKG_TARGET_TRIPLET "arm64-linux")
        else()
            set(VCPKG_TARGET_TRIPLET "x64-linux")
        endif()
    else()
        message(WARNING "Unknown platform, defaulting VCPKG_TARGET_TRIPLET to x64-linux")
        set(VCPKG_TARGET_TRIPLET "x64-linux")
    endif()

    message(STATUS "Auto-detected VCPKG_TARGET_TRIPLET: ${VCPKG_TARGET_TRIPLET}")
endif()

set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg")

# Auto-bootstrap if vcpkg executable doesn't exist
if(NOT EXISTS "${VCPKG_EXECUTABLE}")
    message(STATUS "vcpkg not found at ${VCPKG_EXECUTABLE}, bootstrapping...")

    if(WIN32)
        execute_process(
            COMMAND powershell -ExecutionPolicy Bypass -File bootstrap-vcpkg.bat
            WORKING_DIRECTORY "${VCPKG_ROOT}"
            RESULT_VARIABLE VCPKG_BOOTSTRAP_RESULT
        )
    else()
        execute_process(
            COMMAND ./bootstrap-vcpkg.sh
            WORKING_DIRECTORY "${VCPKG_ROOT}"
            RESULT_VARIABLE VCPKG_BOOTSTRAP_RESULT
        )
    endif()

    if(NOT VCPKG_BOOTSTRAP_RESULT EQUAL 0)
        message(FATAL_ERROR "Failed to bootstrap vcpkg (exit code ${VCPKG_BOOTSTRAP_RESULT})")
    endif()

    message(STATUS "vcpkg bootstrapped successfully")
endif()

# Auto-install dependencies (requires vcpkg.json in project root)
if(EXISTS "${CMAKE_SOURCE_DIR}/vcpkg.json")
    message(STATUS "Found vcpkg.json, installing dependencies...")

    execute_process(
        COMMAND "${VCPKG_EXECUTABLE}" install --triplet ${VCPKG_TARGET_TRIPLET}
        WORKING_DIRECTORY "${CMAKE_SOURCE_DIR}"
        RESULT_VARIABLE VCPKG_INSTALL_RESULT
        OUTPUT_VARIABLE VCPKG_OUTPUT
        ERROR_VARIABLE VCPKG_ERROR
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_STRIP_TRAILING_WHITESPACE
    )

    if(NOT VCPKG_INSTALL_RESULT EQUAL 0)
        message(FATAL_ERROR "vcpkg install failed (exit code ${VCPKG_INSTALL_RESULT}):\n${VCPKG_ERROR}")
    endif()

    message(STATUS "vcpkg dependencies installed successfully")
endif()
