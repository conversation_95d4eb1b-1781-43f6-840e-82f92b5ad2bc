option(VCPKG_AUTO_BOOTSTRAP "Automatically bootstrap vcpkg if not found" ON)
option(VCPKG_AUTO_INSTALL "Automatically install dependencies from vcpkg.json" ON)
option(VCPKG_VERBOSE "Enable verbose vcpkg output" OFF)

# Validate VCPKG_ROOT
if(NOT DEFINED VCPKG_ROOT OR VCPKG_ROOT STREQUAL "")
    message(FATAL_ERROR "VCPKG_ROOT not defined. Set with: cmake -DVCPKG_ROOT=/path/to/vcpkg")
endif()
if(NOT EXISTS "${VCPKG_ROOT}")
    message(FATAL_ERROR "VCPKG_ROOT directory not found: ${VCPKG_ROOT}")
endif()

# Auto-detect target triplet
function(_vcpkg_detect_target_triplet)
    if(DEFINED VCPKG_TARGET_TRIPLET)
        return()
    endif()

    if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        set(detected_triplet "x64-windows")
        if(CMAKE_SIZEOF_VOID_P EQUAL 4)
            set(detected_triplet "x86-windows")
        endif()
    elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
        set(detected_triplet "x64-osx")
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "^(arm64|aarch64)$" OR CMAKE_OSX_ARCHITECTURES MATCHES "arm64")
            set(detected_triplet "arm64-osx")
        endif()
    elseif(CMAKE_SYSTEM_NAME STREQUAL "Linux")
        set(detected_triplet "x64-linux")
        if(CMAKE_SYSTEM_PROCESSOR MATCHES "^(aarch64|arm64)$")
            set(detected_triplet "arm64-linux")
        elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "^(armv7|arm)$")
            set(detected_triplet "arm-linux")
        endif()
    elseif(CMAKE_SYSTEM_NAME STREQUAL "FreeBSD")
        set(detected_triplet "x64-freebsd")
    else()
        message(WARNING "Unknown platform '${CMAKE_SYSTEM_NAME}', using x64-linux")
        set(detected_triplet "x64-linux")
    endif()

    set(VCPKG_TARGET_TRIPLET "${detected_triplet}" PARENT_SCOPE)
endfunction()

_vcpkg_detect_target_triplet()

# Set vcpkg executable path
if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg.exe")
else()
    set(VCPKG_EXECUTABLE "${VCPKG_ROOT}/vcpkg")
endif()

# Bootstrap vcpkg if needed
function(_vcpkg_bootstrap)
    if(NOT VCPKG_AUTO_BOOTSTRAP)
        message(FATAL_ERROR "vcpkg not found at ${VCPKG_EXECUTABLE}. Enable VCPKG_AUTO_BOOTSTRAP or bootstrap manually.")
    endif()

    if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        set(bootstrap_script "${VCPKG_ROOT}/bootstrap-vcpkg.bat")
    else()
        set(bootstrap_script "${VCPKG_ROOT}/bootstrap-vcpkg.sh")
    endif()

    if(NOT EXISTS "${bootstrap_script}")
        message(FATAL_ERROR "Bootstrap script not found: ${bootstrap_script}")
    endif()

    execute_process(
        COMMAND ${bootstrap_script}
        WORKING_DIRECTORY "${VCPKG_ROOT}"
        RESULT_VARIABLE bootstrap_result
        OUTPUT_VARIABLE bootstrap_output
        ERROR_VARIABLE bootstrap_error
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_STRIP_TRAILING_WHITESPACE
    )

    if(NOT bootstrap_result EQUAL 0)
        message(FATAL_ERROR "vcpkg bootstrap failed (${bootstrap_result}): ${bootstrap_error}")
    endif()

    if(NOT EXISTS "${VCPKG_EXECUTABLE}")
        message(FATAL_ERROR "Bootstrap completed but vcpkg executable not found")
    endif()
endfunction()

if(NOT EXISTS "${VCPKG_EXECUTABLE}")
    _vcpkg_bootstrap()
endif()

# Install dependencies from vcpkg.json
function(_vcpkg_install_dependencies)
    if(NOT VCPKG_AUTO_INSTALL)
        return()
    endif()

    set(manifest_file "${CMAKE_SOURCE_DIR}/vcpkg.json")
    if(NOT EXISTS "${manifest_file}")
        return()
    endif()

    # Check if reinstall is needed using cache
    set(cache_dir "${CMAKE_BINARY_DIR}/vcpkg-cache")
    set(install_cache "${cache_dir}/install.stamp")
    file(MAKE_DIRECTORY "${cache_dir}")

    set(need_install FALSE)
    if(NOT EXISTS "${install_cache}")
        set(need_install TRUE)
    else()
        file(TIMESTAMP "${manifest_file}" manifest_time)
        file(TIMESTAMP "${install_cache}" cache_time)
        if(manifest_time IS_NEWER_THAN cache_time)
            set(need_install TRUE)
        endif()
    endif()

    if(NOT need_install)
        return()
    endif()

    set(install_args install --triplet "${VCPKG_TARGET_TRIPLET}")
    if(VCPKG_VERBOSE)
        list(APPEND install_args --verbose)
    endif()

    execute_process(
        COMMAND "${VCPKG_EXECUTABLE}" ${install_args}
        WORKING_DIRECTORY "${CMAKE_SOURCE_DIR}"
        RESULT_VARIABLE install_result
        OUTPUT_VARIABLE install_output
        ERROR_VARIABLE install_error
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_STRIP_TRAILING_WHITESPACE
    )

    if(NOT install_result EQUAL 0)
        message(FATAL_ERROR "vcpkg install failed (${install_result}): ${install_error}")
    endif()

    file(WRITE "${install_cache}" "Dependencies installed")
endfunction()

_vcpkg_install_dependencies()
