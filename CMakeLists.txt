cmake_minimum_required(VERSION 3.25)
include(cmake/vcpkg-toolchain.cmake)

# TODO: Change 'MyProject' to your actual project name
project(
  MyProject
  VERSION 0.1.0
  DESCRIPTION "A modern C++23 project"
  LANGUAGES CXX
)

# --- Configuration (C++ standards, build types, etc.)---
include(cmake/project-options.cmake)

# --- Dependencies ---
include(cmake/dependencies.cmake)

# --- Project Targets (includes directory, interface targets for options and warnings) ---
include(cmake/project-targets.cmake)

# --- Add Source Code ---
add_subdirectory(src)

# --- Testing (if this is the main project) ---
if(PROJECT_IS_TOP_LEVEL)
  include(cmake/testing.cmake)
endif()
