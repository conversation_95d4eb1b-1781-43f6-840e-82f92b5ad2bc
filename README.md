# Modern C++23 CMake Template with Conan

A production-ready C++23 project template featuring Conan package management, automatic IDE integration, and comprehensive testing setup.

## ✨ Features

- **C++23** standard with cross-platform compiler support
- **Conan 2.x integration** for automatic dependency management
- **CMake presets** for standardized build configurations (Debug, Release, RelWithDebInfo)
- **Automatic IDE setup** with compile_commands.json generation
- **Catch2 testing** with zero-configuration setup
- **Comprehensive compiler warnings** and best practices built-in
- **Cross-platform support** (Windows, Linux, macOS)

## � Quick Start

### Prerequisites
- **CMake 3.25+**
- **C++23 compiler** (GCC 11+, Clang 14+, MSVC 2022+)
- **Conan 2.x** (`pip install conan`)

### Setup

```bash
# 1. Clone and customize
git clone <your-repo-url> my-project
cd my-project

# 2. Update project name in CMakeLists.txt 
# Change "MyProject" to your actual project name

# 3. Build and run
cmake --preset Debug
cmake --build build/Debug
./build/Debug/src/MyProject_app

# 4. Run tests
cmake --build build/Debug --target test
```

## 📁 Project Structure

```
├── CMakeLists.txt          # Main CMake configuration
├── CMakePresets.json       # Build presets (Debug, Release, etc.)
├── conanfile.py           # Conan dependencies (Catch2 included)
├── cmake/                 # CMake modules and configuration
├── src/                   # Source files
│   ├── CMakeLists.txt
│   └── main.cpp
└── test/                  # Test files (Catch2)
    ├── CMakeLists.txt
    └── test_main.cpp
```

## 🔧 Build & Test

### Build Configurations

| Preset | Description | Command |
|--------|-------------|---------|
| `Debug` | Development build with debug symbols | `cmake --preset Debug` |
| `Release` | Optimized production build | `cmake --preset Release` |
| `RelWithDebInfo` | Optimized with debug symbols | `cmake --preset RelWithDebInfo` |

### Building

```bash
# Configure and build
cmake --preset Debug
cmake --build build/Debug

# Different configurations
cmake --preset Release && cmake --build build/Release
```

### Testing with Catch2

```bash
# Run all tests
cmake --build build/Debug --target test

# Run tests with output
cd build/Debug && ctest --output-on-failure

# Run specific test executable
./build/Debug/test/MyProject_tests
```

## 💻 IDE Integration

### VS Code + clangd (Recommended)
1. Install **clangd extension** (disable C/C++ extension)
2. Build project once: `cmake --preset Debug && cmake --build build/Debug`
Note: generates `compile_commands.json` symlink for clangd language server

### CLion
1. Open project folder
2. CLion auto-detects CMake configuration
3. Select preset from CMake profiles dropdown

### Other IDEs
Any IDE supporting `compile_commands.json` works automatically (Qt Creator, Code::Blocks, etc.)

## ⚙️ Basic Customization

### 1. Rename Project
Edit `CMakeLists.txt` line 4-8:
```cmake
project(
  YourProjectName              # Change this
  VERSION 1.0.0
  DESCRIPTION "Your description"
  LANGUAGES CXX
)
```

### 2. Add Source Files
Edit `src/CMakeLists.txt`:
```cmake
add_executable(${PROJECT_NAME}_app
    main.cpp
    your_file.cpp               # Add new files
    another_file.cpp
)
```

### 3. Add Dependencies
Edit `conanfile.py`:
```python
def requirements(self):
    self.requires("catch2/3.8.1")
    self.requires("fmt/10.2.1")      # Add new dependencies
    self.requires("spdlog/1.12.0")
```
 
 Edit `cmake/dependencies.cmake`:
```cmake
find_package(fmt REQUIRED)
find_package(spdlog REQUIRED)
```

### 4. Link Dependencies
Update `src/CMakeLists.txt`:
```cmake
target_link_libraries(${PROJECT_NAME}_app
  PRIVATE
    ${PROJECT_NAME}_options
    ${PROJECT_NAME}_warnings
    fmt::fmt                    # Link new dependencies
    spdlog::spdlog
)
```

## � Common Issues

### "Conan not found"
```bash
pip install conan
conan profile detect --force
```

### "CMake version too old"
```bash
# Ubuntu/Debian
sudo apt install cmake

# macOS
brew install cmake
```

### "C++23 not supported"
Update your compiler:
- **GCC**: 11+ required
- **Clang**: 14+ required
- **MSVC**: Visual Studio 2022+ required

### "compile_commands.json missing"
Build the project once:
```bash
cmake --preset Debug
cmake --build build/Debug
```

## 📚 Next Steps

- **Add your source code** to `src/`
- **Write tests** in `test/` using Catch2
- **Add dependencies** via `conanfile.py`
- **Configure CI/CD** with provided CMake presets
- **Customize compiler warnings** in `cmake/project-warnings.cmake`

---
