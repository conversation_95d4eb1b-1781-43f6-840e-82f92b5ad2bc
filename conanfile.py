from conan import ConanFile
from conan.tools.cmake import cmake_layout, CMakeToolchain, CMakeDeps

class ProjectConan(ConanFile):
    settings = "os", "compiler", "build_type", "arch"
    
    def requirements(self):
        # Fetch Catch2 for testing
        self.requires("catch2/3.8.1")

        # Fetch packages here. For example: 
        # self.requires("fmt/10.2.1")
        self.requires("sfml/2.6.2")
    
    def generate(self):
        deps = CMakeDeps(self)
        deps.generate()
        
        tc = CMakeToolchain(self)
        tc.user_presets_path = False  # disable CMakeUserPresets.json generation
        tc.generate()
    
    def layout(self):
        cmake_layout(self)

    def configure(self):
        self.settings.compiler.cppstd = "17"
